/**
 * Blog Search Filter Addon Styles
 * Custom CSS for SP Page Builder Blog Search Filter
 */

/* Filter Container */
.sppb-addon-blog-search-filter {
    margin-bottom: 30px;
}

.sppb-addon-blog-search-filter .filter-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    position: relative;
}

/* Filter Title */
.sppb-addon-blog-search-filter .filter-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

/* Filter Groups */
.sppb-addon-blog-search-filter .filter-group {
    margin-bottom: 25px;
}

.sppb-addon-blog-search-filter .filter-group:last-of-type {
    margin-bottom: 20px;
}

.sppb-addon-blog-search-filter .filter-group-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
    display: flex;
    align-items: center;
}

.sppb-addon-blog-search-filter .filter-group-title:before {
    content: '';
    width: 4px;
    height: 16px;
    background: #007bff;
    margin-right: 8px;
    border-radius: 2px;
}

/* Search Input */
.sppb-addon-blog-search-filter .search-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.sppb-addon-blog-search-filter .search-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.sppb-addon-blog-search-filter .search-input::placeholder {
    color: #999;
    font-style: italic;
}

/* Filter Options */
.sppb-addon-blog-search-filter .filter-options {
    max-height: 200px;
    overflow-y: auto;
    padding-right: 5px;
}

.sppb-addon-blog-search-filter .filter-options::-webkit-scrollbar {
    width: 6px;
}

.sppb-addon-blog-search-filter .filter-options::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.sppb-addon-blog-search-filter .filter-options::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sppb-addon-blog-search-filter .filter-options::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Filter Option Items */
.sppb-addon-blog-search-filter .filter-option {
    margin-bottom: 10px;
}

.sppb-addon-blog-search-filter .filter-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
    padding: 6px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    margin: 0;
}

.sppb-addon-blog-search-filter .filter-label:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.sppb-addon-blog-search-filter .filter-checkbox,
.sppb-addon-blog-search-filter .filter-radio {
    margin-right: 10px;
    margin-top: 0;
    margin-bottom: 0;
    width: 16px;
    height: 16px;
    accent-color: #007bff;
}

.sppb-addon-blog-search-filter .filter-text {
    flex: 1;
    color: #555;
    transition: color 0.2s ease;
}

.sppb-addon-blog-search-filter .filter-label:hover .filter-text {
    color: #007bff;
}

/* Filter Buttons */
.sppb-addon-blog-search-filter .filter-buttons {
    margin-top: 25px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.sppb-addon-blog-search-filter .filter-submit {
    background: #007bff;
    color: white;
    border: 1px solid #007bff;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 120px;
}

.sppb-addon-blog-search-filter .filter-submit:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
}

.sppb-addon-blog-search-filter .filter-clear {
    background: #6c757d;
    color: white;
    border: 1px solid #6c757d;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 120px;
}

.sppb-addon-blog-search-filter .filter-clear:hover {
    background: #545b62;
    border-color: #545b62;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.2);
}

/* Loading States */
.sppb-addon-blog-search-filter .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 8px;
}

.sppb-addon-blog-search-filter .spinner {
    padding: 20px;
    font-size: 14px;
    color: #007bff;
    font-weight: 500;
}

.sppb-addon-blog-search-filter .spinner:before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sppb-addon-blog-search-filter .filter-container {
        padding: 15px;
    }
    
    .sppb-addon-blog-search-filter .filter-title {
        font-size: 18px;
        margin-bottom: 15px;
    }
    
    .sppb-addon-blog-search-filter .filter-group {
        margin-bottom: 20px;
    }
    
    .sppb-addon-blog-search-filter .filter-buttons {
        flex-direction: column;
        gap: 8px;
    }
    
    .sppb-addon-blog-search-filter .filter-submit,
    .sppb-addon-blog-search-filter .filter-clear {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .sppb-addon-blog-search-filter .filter-container {
        padding: 12px;
    }
    
    .sppb-addon-blog-search-filter .search-input {
        padding: 10px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .sppb-addon-blog-search-filter .filter-options {
        max-height: 150px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .sppb-addon-blog-search-filter .filter-container {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .sppb-addon-blog-search-filter .filter-title,
    .sppb-addon-blog-search-filter .filter-group-title {
        color: #e2e8f0;
    }
    
    .sppb-addon-blog-search-filter .search-input {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .sppb-addon-blog-search-filter .search-input::placeholder {
        color: #a0aec0;
    }
    
    .sppb-addon-blog-search-filter .filter-text {
        color: #cbd5e0;
    }
    
    .sppb-addon-blog-search-filter .filter-label:hover {
        background-color: rgba(0, 123, 255, 0.1);
    }
}

/* Print Styles */
@media print {
    .sppb-addon-blog-search-filter {
        display: none;
    }
}
