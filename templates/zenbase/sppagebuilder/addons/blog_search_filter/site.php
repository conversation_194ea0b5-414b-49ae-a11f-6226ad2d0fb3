<?php
/**
 * @package SP Page Builder
 * @subpackage Blog Search Filter Addon
 * <AUTHOR> Development
 */

defined('_JEXEC') or die('Restricted access');

use Jo<PERSON><PERSON>\CMS\Factory;
use Joomla\CMS\Language\Text;
use Jo<PERSON><PERSON>\CMS\HTML\HTMLHelper;

class SppagebuilderAddonBlog_search_filter extends SppagebuilderAddons
{
    public function render()
    {
        $settings = $this->addon->settings;
        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        
        // Load required assets
        $this->loadAssets();
        
        // Get filter data
        $categories = $this->getCategories();
        $tags = $this->getTags();
        
        // Build CSS styles
        $css = $this->buildCSS($settings);
        
        $output = '<style>' . $css . '</style>';
        $output .= '<div class="sppb-addon sppb-addon-blog-search-filter ' . $class . '" id="blog-search-filter-' . $this->addon->id . '">';
        
        // Title
        if (isset($settings->title) && $settings->title) {
            $output .= '<h3 class="sppb-addon-title filter-title">' . $settings->title . '</h3>';
        }
        
        $output .= '<div class="sppb-addon-content filter-container">';
        $output .= '<form class="blog-search-filter-form" data-target="' . (isset($settings->target_results_id) ? $settings->target_results_id : 'blog-results') . '" data-ajax="' . (isset($settings->ajax_enabled) ? $settings->ajax_enabled : '1') . '">';
        
        // Search box
        if (isset($settings->show_search_box) && $settings->show_search_box) {
            $placeholder = isset($settings->search_placeholder) ? $settings->search_placeholder : 'Enter keywords...';
            $output .= '<div class="filter-group search-group">';
            $output .= '<input type="text" name="search" class="form-control search-input" placeholder="' . $placeholder . '" autocomplete="off">';
            $output .= '</div>';
        }
        
        // Categories filter
        if (isset($settings->show_categories) && $settings->show_categories && !empty($categories)) {
            $categoriesTitle = isset($settings->categories_title) ? $settings->categories_title : 'Categories';
            $output .= '<div class="filter-group categories-group">';
            $output .= '<h4 class="filter-group-title">' . $categoriesTitle . '</h4>';
            $output .= '<div class="filter-options">';
            
            foreach ($categories as $category) {
                $output .= '<div class="filter-option">';
                $output .= '<label class="filter-label">';
                $output .= '<input type="checkbox" name="categories[]" value="' . $category->id . '" class="filter-checkbox"> ';
                $output .= '<span class="filter-text">' . $category->title . '</span>';
                $output .= '</label>';
                $output .= '</div>';
            }
            
            $output .= '</div>';
            $output .= '</div>';
        }
        
        // Tags filter
        if (isset($settings->show_tags) && $settings->show_tags && !empty($tags)) {
            $tagsTitle = isset($settings->tags_title) ? $settings->tags_title : 'Tags';
            $output .= '<div class="filter-group tags-group">';
            $output .= '<h4 class="filter-group-title">' . $tagsTitle . '</h4>';
            $output .= '<div class="filter-options">';
            
            foreach ($tags as $tag) {
                $output .= '<div class="filter-option">';
                $output .= '<label class="filter-label">';
                $output .= '<input type="checkbox" name="tags[]" value="' . $tag->id . '" class="filter-checkbox"> ';
                $output .= '<span class="filter-text">' . $tag->title . '</span>';
                $output .= '</label>';
                $output .= '</div>';
            }
            
            $output .= '</div>';
            $output .= '</div>';
        }
        
        // Date filter
        if (isset($settings->show_date_filter) && $settings->show_date_filter) {
            $output .= '<div class="filter-group date-group">';
            $output .= '<h4 class="filter-group-title">Date Range</h4>';
            $output .= '<div class="filter-options">';
            
            $dateOptions = [
                'last_week' => 'Last Week',
                'last_month' => 'Last Month',
                'last_3_months' => 'Last 3 Months',
                'last_year' => 'Last Year'
            ];
            
            foreach ($dateOptions as $value => $label) {
                $output .= '<div class="filter-option">';
                $output .= '<label class="filter-label">';
                $output .= '<input type="radio" name="date_range" value="' . $value . '" class="filter-radio"> ';
                $output .= '<span class="filter-text">' . $label . '</span>';
                $output .= '</label>';
                $output .= '</div>';
            }
            
            $output .= '</div>';
            $output .= '</div>';
        }
        
        // Filter buttons
        $filterButtonText = isset($settings->filter_button_text) ? $settings->filter_button_text : 'Filter Results';
        $clearButtonText = isset($settings->clear_button_text) ? $settings->clear_button_text : 'Clear Filters';
        
        $output .= '<div class="filter-buttons">';
        $output .= '<button type="submit" class="btn btn-primary filter-submit">' . $filterButtonText . '</button>';
        $output .= '<button type="reset" class="btn btn-secondary filter-clear">' . $clearButtonText . '</button>';
        $output .= '</div>';
        
        $output .= '</form>';
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    private function loadAssets()
    {
        // Load jQuery if not already loaded
        HTMLHelper::_('jquery.framework');

        // Add custom JavaScript with improved functionality
        $js = "
        jQuery(document).ready(function($) {
            $('.blog-search-filter-form').on('submit', function(e) {
                e.preventDefault();

                var form = $(this);
                var targetId = form.data('target');
                var ajaxEnabled = form.data('ajax');
                var formData = form.serialize();

                if (ajaxEnabled == '1') {
                    // AJAX filtering
                    var targetElement = $('#' + targetId);
                    targetElement.addClass('loading');

                    // Add loading overlay if not exists
                    if (targetElement.find('.loading-overlay').length === 0) {
                        targetElement.append('<div class=\"loading-overlay\" style=\"position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(255,255,255,0.8);display:flex;align-items:center;justify-content:center;z-index:1000;\"><div class=\"spinner\">Loading...</div></div>');
                    }

                    $.ajax({
                        url: 'index.php?option=com_ajax&plugin=blog_filter&format=json',
                        type: 'POST',
                        data: formData + '&action=filter_articles',
                        success: function(response) {
                            if (response.success) {
                                targetElement.html(response.data);
                                // Trigger custom event for other scripts
                                targetElement.trigger('blog-filtered', [response]);
                            } else {
                                console.error('Filter error:', response.message || 'Unknown error');
                                targetElement.find('.loading-overlay').remove();
                            }
                            targetElement.removeClass('loading');
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX error:', error);
                            targetElement.removeClass('loading');
                            targetElement.find('.loading-overlay').remove();
                        }
                    });
                } else {
                    // Standard form submission with URL parameters
                    var url = window.location.pathname + '?' + formData;
                    window.location.href = url;
                }
            });

            // Clear filters
            $('.blog-search-filter-form .filter-clear').on('click', function(e) {
                e.preventDefault();
                var form = $(this).closest('form');
                form[0].reset();

                // Trigger filter to show all results
                if (form.data('ajax') == '1') {
                    form.trigger('submit');
                }
            });

            // Live search with debouncing
            var searchTimeout;
            $('.search-input').on('keyup', function() {
                var form = $(this).closest('form');
                if (form.data('ajax') == '1') {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(function() {
                        form.trigger('submit');
                    }, 750); // Increased delay for better UX
                }
            });

            // Auto-submit on checkbox/radio changes
            $('.blog-search-filter-form input[type=\"checkbox\"], .blog-search-filter-form input[type=\"radio\"]').on('change', function() {
                var form = $(this).closest('form');
                if (form.data('ajax') == '1') {
                    form.trigger('submit');
                }
            });
        });
        ";

        $doc = Factory::getDocument();
        $doc->addScriptDeclaration($js);
    }
    
    private function getCategories()
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true)
            ->select('id, title, alias')
            ->from('#__categories')
            ->where('extension = ' . $db->quote('com_content'))
            ->where('published = 1')
            ->where('level > 0')
            ->order('title ASC');
        
        try {
            return $db->setQuery($query)->loadObjectList();
        } catch (Exception $e) {
            return array();
        }
    }
    
    private function getTags()
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true)
            ->select('id, title, alias')
            ->from('#__tags')
            ->where('published = 1')
            ->where('level > 0')
            ->order('title ASC')
            ->setLimit(20); // Limit to prevent too many tags
        
        try {
            return $db->setQuery($query)->loadObjectList();
        } catch (Exception $e) {
            return array();
        }
    }
    
    private function buildCSS($settings)
    {
        $css = '';
        $addonId = $this->addon->id;
        
        // Container styles
        $background = isset($settings->filter_background) ? $settings->filter_background : '#f8f9fa';
        $border = isset($settings->filter_border) ? $settings->filter_border : '#dee2e6';
        $padding = isset($settings->filter_padding) ? $settings->filter_padding : 20;
        
        $css .= "#blog-search-filter-{$addonId} .filter-container {
            background-color: {$background};
            border: 1px solid {$border};
            padding: {$padding}px;
            border-radius: 8px;
            margin-bottom: 20px;
        }";
        
        // Button styles
        $buttonColor = isset($settings->button_color) ? $settings->button_color : '#007bff';
        $buttonHover = isset($settings->button_hover_color) ? $settings->button_hover_color : '#0056b3';
        
        $css .= "#blog-search-filter-{$addonId} .filter-submit {
            background-color: {$buttonColor};
            border-color: {$buttonColor};
        }";
        
        $css .= "#blog-search-filter-{$addonId} .filter-submit:hover {
            background-color: {$buttonHover};
            border-color: {$buttonHover};
        }";
        
        // Additional styling
        $css .= "#blog-search-filter-{$addonId} .filter-group {
            margin-bottom: 20px;
        }
        
        #blog-search-filter-{$addonId} .filter-group-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        #blog-search-filter-{$addonId} .filter-option {
            margin-bottom: 8px;
        }
        
        #blog-search-filter-{$addonId} .filter-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
        }
        
        #blog-search-filter-{$addonId} .filter-checkbox,
        #blog-search-filter-{$addonId} .filter-radio {
            margin-right: 8px;
        }
        
        #blog-search-filter-{$addonId} .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        #blog-search-filter-{$addonId} .filter-buttons {
            margin-top: 20px;
        }
        
        #blog-search-filter-{$addonId} .filter-buttons .btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        #blog-search-filter-{$addonId} .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }";
        
        return $css;
    }
}
