<?php
/**
 * @package SP Page Builder
 * @subpackage Blog Grid Cards Addon
 * <AUTHOR> Development
 */

defined('_JEXEC') or die('Restricted access');

SpAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'blog_grid_cards',
        'title' => JText::_('Blog Grid Cards'),
        'desc' => JText::_('Display blog articles in a clean grid card layout without body text'),
        'category' => 'Content',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_SPPAGEBUILDER_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_SPPAGEBUILDER_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('Section Title'),
                    'desc' => JText::_('Enter title for the blog grid section'),
                    'std' => ''
                ),
                
                'catid' => array(
                    'type' => 'category',
                    'title' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_CATID'),
                    'desc' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_CATID_DESC'),
                    'multiple' => true,
                ),
                
                'tagids' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_TAGS'),
                    'desc' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_TAGS_DESC'),
                    'values' => SpPgaeBuilderBase::getArticleTags(),
                    'multiple' => true,
                ),
                
                'ordering' => array(
                    'type' => 'select',
                    'title' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_ORDERING'),
                    'desc' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_ORDERING_DESC'),
                    'values' => array(
                        'latest' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_ORDERING_LATEST'),
                        'oldest' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_ORDERING_OLDEST'),
                        'hits' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_ORDERING_POPULAR'),
                        'featured' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_ORDERING_FEATURED'),
                        'random' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_ORDERING_RANDOM'),
                    ),
                    'std' => 'latest',
                ),
                
                'limit' => array(
                    'type' => 'number',
                    'title' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_LIMIT'),
                    'desc' => JText::_('COM_SPPAGEBUILDER_ADDON_ARTICLES_LIMIT_DESC'),
                    'std' => '9'
                ),
                
                'columns' => array(
                    'type' => 'select',
                    'title' => JText::_('Grid Columns'),
                    'desc' => JText::_('Number of columns in the grid layout'),
                    'values' => array(
                        '2' => '2 Columns',
                        '3' => '3 Columns',
                        '4' => '4 Columns',
                        '6' => '6 Columns'
                    ),
                    'std' => '3',
                ),
                
                'show_featured_image' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Featured Image'),
                    'desc' => JText::_('Display article featured images in cards'),
                    'std' => 1
                ),
                
                'image_height' => array(
                    'type' => 'slider',
                    'title' => JText::_('Image Height'),
                    'desc' => JText::_('Fixed height for card images in pixels'),
                    'std' => 200,
                    'min' => 100,
                    'max' => 400,
                    'depends' => array('show_featured_image' => '1')
                ),
                
                'show_author' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Author'),
                    'desc' => JText::_('Display article author in card meta'),
                    'std' => 1
                ),
                
                'show_date' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Date'),
                    'desc' => JText::_('Display article date in card meta'),
                    'std' => 1
                ),
                
                'show_category' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Category'),
                    'desc' => JText::_('Display article category in card meta'),
                    'std' => 0
                ),
                
                'show_readmore' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Read More Button'),
                    'desc' => JText::_('Display read more button on cards'),
                    'std' => 1
                ),
                
                'readmore_text' => array(
                    'type' => 'text',
                    'title' => JText::_('Read More Text'),
                    'desc' => JText::_('Text for the read more button'),
                    'std' => 'Read More',
                    'depends' => array('show_readmore' => '1')
                ),
                
                'show_filter' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Search Filter'),
                    'desc' => JText::_('Display integrated search and filter above the grid'),
                    'std' => 1
                ),

                'filter_position' => array(
                    'type' => 'select',
                    'title' => JText::_('Filter Position'),
                    'desc' => JText::_('Position of the search filter relative to the grid'),
                    'values' => array(
                        'top' => 'Above Grid',
                        'top-left' => 'Top Left',
                        'top-right' => 'Top Right'
                    ),
                    'std' => 'top',
                    'depends' => array('show_filter' => '1')
                ),

                'filter_style' => array(
                    'type' => 'select',
                    'title' => JText::_('Filter Style'),
                    'desc' => JText::_('Visual style of the filter interface'),
                    'values' => array(
                        'compact' => 'Compact Horizontal',
                        'expanded' => 'Expanded Vertical',
                        'minimal' => 'Minimal Search Only'
                    ),
                    'std' => 'compact',
                    'depends' => array('show_filter' => '1')
                ),

                'show_search_box' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Search Box'),
                    'desc' => JText::_('Display text search input field'),
                    'std' => 1,
                    'depends' => array('show_filter' => '1')
                ),

                'show_category_filter' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Category Filter'),
                    'desc' => JText::_('Display category dropdown/checkboxes'),
                    'std' => 1,
                    'depends' => array('show_filter' => '1')
                ),

                'show_tag_filter' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Tag Filter'),
                    'desc' => JText::_('Display tag dropdown/checkboxes'),
                    'std' => 0,
                    'depends' => array('show_filter' => '1')
                ),

                'show_date_filter' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Date Filter'),
                    'desc' => JText::_('Display date range filter options'),
                    'std' => 0,
                    'depends' => array('show_filter' => '1')
                ),

                'ajax_filtering' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Enable AJAX Filtering'),
                    'desc' => JText::_('Filter results without page reload (requires AJAX plugin)'),
                    'std' => 1,
                    'depends' => array('show_filter' => '1')
                )
            ),
            
            'styling' => array(
                'card_background' => array(
                    'type' => 'color',
                    'title' => JText::_('Card Background'),
                    'desc' => JText::_('Background color for cards'),
                    'std' => '#ffffff'
                ),
                
                'card_border' => array(
                    'type' => 'color',
                    'title' => JText::_('Card Border'),
                    'desc' => JText::_('Border color for cards'),
                    'std' => '#e9ecef'
                ),
                
                'card_shadow' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Card Shadow'),
                    'desc' => JText::_('Add shadow effect to cards'),
                    'std' => 1
                ),
                
                'card_hover_effect' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Card Hover Effect'),
                    'desc' => JText::_('Add hover animation to cards'),
                    'std' => 1
                ),
                
                'card_padding' => array(
                    'type' => 'slider',
                    'title' => JText::_('Card Content Padding'),
                    'desc' => JText::_('Padding inside card content area'),
                    'std' => 20,
                    'max' => 50,
                    'responsive' => true
                ),
                
                'card_gap' => array(
                    'type' => 'slider',
                    'title' => JText::_('Card Gap'),
                    'desc' => JText::_('Space between cards'),
                    'std' => 20,
                    'max' => 50,
                    'responsive' => true
                ),
                
                'title_color' => array(
                    'type' => 'color',
                    'title' => JText::_('Title Color'),
                    'desc' => JText::_('Color for article titles'),
                    'std' => '#333333'
                ),
                
                'title_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('Title Hover Color'),
                    'desc' => JText::_('Hover color for article titles'),
                    'std' => '#007bff'
                ),
                
                'meta_color' => array(
                    'type' => 'color',
                    'title' => JText::_('Meta Text Color'),
                    'desc' => JText::_('Color for meta information (author, date)'),
                    'std' => '#6c757d'
                ),
                
                'button_color' => array(
                    'type' => 'color',
                    'title' => JText::_('Button Color'),
                    'desc' => JText::_('Color for read more buttons'),
                    'std' => '#007bff'
                ),
                
                'button_hover_color' => array(
                    'type' => 'color',
                    'title' => JText::_('Button Hover Color'),
                    'desc' => JText::_('Hover color for read more buttons'),
                    'std' => '#0056b3'
                )
            )
        )
    )
);
