/**
 * Blog Grid Cards Addon Styles
 * Custom CSS for SP Page Builder Blog Grid Cards
 */

/* Grid Container */
.sppb-addon-blog-grid-cards {
    margin-bottom: 40px;
}

.sppb-addon-blog-grid-cards .blog-grid-title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 30px;
    color: #333;
    text-align: center;
}

.sppb-addon-blog-grid-cards .blog-grid-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.sppb-addon-blog-grid-cards .blog-grid-col {
    padding: 0 15px;
    margin-bottom: 30px;
}

/* Blog Card Base Styles */
.sppb-addon-blog-grid-cards .blog-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
}

.sppb-addon-blog-grid-cards .blog-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

/* Card Image */
.sppb-addon-blog-grid-cards .blog-card-image {
    flex-shrink: 0;
    overflow: hidden;
    position: relative;
    background: #f8f9fa;
}

.sppb-addon-blog-grid-cards .blog-card-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.4s ease;
    display: block;
}

.sppb-addon-blog-grid-cards .blog-card:hover .blog-card-image img {
    transform: scale(1.08);
}

.sppb-addon-blog-grid-cards .blog-card-image a {
    display: block;
    position: relative;
    overflow: hidden;
}

.sppb-addon-blog-grid-cards .blog-card-image a:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0) 0%, rgba(0, 123, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sppb-addon-blog-grid-cards .blog-card:hover .blog-card-image a:after {
    opacity: 1;
}

/* Card Content */
.sppb-addon-blog-grid-cards .blog-card-content {
    padding: 24px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Card Title */
.sppb-addon-blog-grid-cards .blog-card-title {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.4;
    flex-grow: 1;
    min-height: 56px; /* Ensures consistent height */
}

.sppb-addon-blog-grid-cards .blog-card-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.sppb-addon-blog-grid-cards .blog-card-title a:hover {
    color: #007bff;
}

/* Card Meta */
.sppb-addon-blog-grid-cards .blog-card-meta {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 16px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.sppb-addon-blog-grid-cards .blog-card-meta span {
    display: inline-flex;
    align-items: center;
    font-size: 13px;
}

.sppb-addon-blog-grid-cards .blog-card-meta span:not(:last-child):after {
    content: '•';
    margin-left: 8px;
    color: #dee2e6;
    font-weight: bold;
}

.sppb-addon-blog-grid-cards .blog-card-author:before {
    content: '👤';
    margin-right: 4px;
    font-size: 12px;
}

.sppb-addon-blog-grid-cards .blog-card-date:before {
    content: '📅';
    margin-right: 4px;
    font-size: 12px;
}

.sppb-addon-blog-grid-cards .blog-card-category a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.sppb-addon-blog-grid-cards .blog-card-category a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.sppb-addon-blog-grid-cards .blog-card-category:before {
    content: '📂';
    margin-right: 4px;
    font-size: 12px;
}

/* Read More Button */
.sppb-addon-blog-grid-cards .blog-card-readmore {
    margin-top: auto;
    padding-top: 8px;
}

.sppb-addon-blog-grid-cards .blog-card-readmore .btn {
    background: #007bff;
    color: white;
    border: 1px solid #007bff;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sppb-addon-blog-grid-cards .blog-card-readmore .btn:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.sppb-addon-blog-grid-cards .blog-card-readmore .btn:after {
    content: '→';
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.sppb-addon-blog-grid-cards .blog-card-readmore .btn:hover:after {
    transform: translateX(4px);
}

/* Loading States */
.sppb-addon-blog-grid-cards.loading {
    position: relative;
    opacity: 0.7;
}

.sppb-addon-blog-grid-cards .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 12px;
}

/* Grid Responsive Layouts */
/* 6 columns */
.sppb-addon-blog-grid-cards .sppb-col-md-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
}

/* 4 columns */
.sppb-addon-blog-grid-cards .sppb-col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
}

/* 3 columns */
.sppb-addon-blog-grid-cards .sppb-col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

/* 2 columns */
.sppb-addon-blog-grid-cards .sppb-col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .sppb-addon-blog-grid-cards .sppb-col-md-2 {
        flex: 0 0 25%;
        max-width: 25%;
    }
}

@media (max-width: 992px) {
    .sppb-addon-blog-grid-cards .sppb-col-md-2,
    .sppb-addon-blog-grid-cards .sppb-col-md-3 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
}

@media (max-width: 768px) {
    .sppb-addon-blog-grid-cards .blog-grid-row {
        margin: 0 -10px;
    }
    
    .sppb-addon-blog-grid-cards .blog-grid-col {
        padding: 0 10px;
        margin-bottom: 20px;
    }
    
    .sppb-addon-blog-grid-cards .sppb-col-md-2,
    .sppb-addon-blog-grid-cards .sppb-col-md-3,
    .sppb-addon-blog-grid-cards .sppb-col-md-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-content {
        padding: 20px;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-title {
        font-size: 18px;
        min-height: 48px;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-image img {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .sppb-addon-blog-grid-cards .sppb-col-md-2,
    .sppb-addon-blog-grid-cards .sppb-col-md-3,
    .sppb-addon-blog-grid-cards .sppb-col-md-4,
    .sppb-addon-blog-grid-cards .sppb-col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .sppb-addon-blog-grid-cards .blog-grid-title {
        font-size: 24px;
        margin-bottom: 20px;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-content {
        padding: 16px;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-title {
        font-size: 16px;
        min-height: auto;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-image img {
        height: 160px;
    }
}

/* No Results State */
.sppb-addon-blog-grid-cards .no-results {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.sppb-addon-blog-grid-cards .no-results h3 {
    font-size: 24px;
    margin-bottom: 12px;
    color: #495057;
}

.sppb-addon-blog-grid-cards .no-results p {
    font-size: 16px;
    margin-bottom: 0;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .sppb-addon-blog-grid-cards .blog-card {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-title a {
        color: #e2e8f0;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-meta {
        color: #a0aec0;
        border-color: #4a5568;
    }
    
    .sppb-addon-blog-grid-cards .blog-grid-title {
        color: #e2e8f0;
    }
}

/* Print Styles */
@media print {
    .sppb-addon-blog-grid-cards .blog-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .sppb-addon-blog-grid-cards .blog-card-readmore {
        display: none;
    }
}
