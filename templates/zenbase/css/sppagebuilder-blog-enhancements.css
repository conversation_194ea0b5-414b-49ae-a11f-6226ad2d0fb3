/**
 * SP Page Builder Blog Enhancements
 * Additional CSS for blog functionality integration
 */

/* Global Blog Layout Improvements */
.sppb-section .blog-layout-wrapper {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.sppb-section .blog-sidebar {
    flex: 0 0 300px;
    position: sticky;
    top: 20px;
}

.sppb-section .blog-content {
    flex: 1;
    min-width: 0;
}

/* Enhanced Articles Addon Styling (for existing articles addon) */
.sppb-addon-articles .sppb-article-introtext {
    display: none !important;
}

.sppb-addon-articles .sppb-addon-article {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
}

.sppb-addon-articles .sppb-addon-article:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-4px);
    border-color: #007bff;
}

.sppb-addon-articles .sppb-article-image {
    flex-shrink: 0;
    overflow: hidden;
}

.sppb-addon-articles .sppb-article-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.sppb-addon-articles .sppb-addon-article:hover .sppb-article-image img {
    transform: scale(1.05);
}

.sppb-addon-articles .sppb-article-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.sppb-addon-articles .sppb-article-title {
    margin: 0 0 0.75rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    flex-grow: 1;
    line-height: 1.4;
}

.sppb-addon-articles .sppb-article-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sppb-addon-articles .sppb-article-title a:hover {
    color: #007bff;
}

.sppb-addon-articles .sppb-article-meta {
    margin-top: auto;
    font-size: 0.875rem;
    color: #6c757d;
    border-top: 1px solid #f0f0f0;
    padding-top: 0.75rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.sppb-addon-articles .sppb-article-readmore {
    margin-top: 1rem;
}

.sppb-addon-articles .sppb-article-readmore .btn {
    background: #007bff;
    border-color: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.sppb-addon-articles .sppb-article-readmore .btn:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
}

/* Blog Template Specific Enhancements */
.blog-template-sidebar .sppb-section {
    padding: 2rem 0;
}

.blog-template-full .sppb-section {
    padding: 3rem 0;
}

.blog-template-featured .featured-section {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.blog-template-minimal .sppb-section {
    padding: 4rem 0;
    max-width: 1000px;
    margin: 0 auto;
}

/* Search Integration Styles */
.blog-search-active .blog-results {
    position: relative;
    min-height: 200px;
}

.blog-search-active .blog-results.loading {
    opacity: 0.6;
    pointer-events: none;
}

.blog-search-active .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.blog-search-active .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Filter Results Animation */
.blog-results .blog-grid-row {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Blog Layout */
@media (max-width: 992px) {
    .sppb-section .blog-layout-wrapper {
        flex-direction: column;
        gap: 20px;
    }
    
    .sppb-section .blog-sidebar {
        flex: none;
        position: static;
        order: -1;
    }
    
    .sppb-addon-articles .sppb-article-content {
        padding: 1.25rem;
    }
    
    .sppb-addon-articles .sppb-article-image img {
        height: 180px;
    }
}

@media (max-width: 768px) {
    .blog-template-sidebar .sppb-section,
    .blog-template-full .sppb-section {
        padding: 1.5rem 0;
    }
    
    .blog-template-minimal .sppb-section {
        padding: 2rem 0;
    }
    
    .sppb-addon-articles .sppb-article-content {
        padding: 1rem;
    }
    
    .sppb-addon-articles .sppb-article-title {
        font-size: 1.125rem;
    }
    
    .sppb-addon-articles .sppb-article-image img {
        height: 160px;
    }
}

@media (max-width: 576px) {
    .sppb-section .blog-layout-wrapper {
        gap: 15px;
    }
    
    .sppb-addon-articles .sppb-article-content {
        padding: 0.875rem;
    }
    
    .sppb-addon-articles .sppb-article-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .sppb-addon-articles .sppb-article-meta {
        font-size: 0.8125rem;
        padding-top: 0.5rem;
    }
}

/* Accessibility Improvements */
.sppb-addon-articles .sppb-addon-article:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.sppb-addon-blog-search-filter .filter-label:focus-within {
    background-color: rgba(0, 123, 255, 0.1);
    outline: 2px solid #007bff;
    outline-offset: 1px;
    border-radius: 4px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .sppb-addon-articles .sppb-addon-article {
        border-width: 2px;
        border-color: #000;
    }
    
    .sppb-addon-articles .sppb-article-title a {
        color: #000;
        font-weight: 700;
    }
    
    .sppb-addon-articles .sppb-article-readmore .btn {
        border-width: 2px;
        font-weight: 600;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .sppb-addon-articles .sppb-addon-article,
    .sppb-addon-articles .sppb-article-image img,
    .sppb-addon-articles .sppb-article-readmore .btn,
    .blog-results .blog-grid-row {
        transition: none;
        animation: none;
        transform: none;
    }
    
    .sppb-addon-articles .sppb-addon-article:hover {
        transform: none;
    }
    
    .sppb-addon-articles .sppb-addon-article:hover .sppb-article-image img {
        transform: none;
    }
}

/* Print Styles */
@media print {
    .sppb-addon-blog-search-filter,
    .sppb-addon-articles .sppb-article-readmore {
        display: none;
    }
    
    .sppb-addon-articles .sppb-addon-article {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
        margin-bottom: 1rem;
    }
    
    .sppb-addon-articles .sppb-article-title a {
        color: #000;
        text-decoration: underline;
    }
}
