# Blog/Knowledge Centre Template Analysis & Integration Guide

## 📋 Overview

This document analyzes the existing templates used for blog and knowledge centre pages on the EverTrek site and provides integration strategies for the new SP Page Builder blog system with integrated filtering.

## �️ Database Locations

### Core Database Tables

#### **Menu Items Table (`ev_menu`)**
```sql
-- Table structure for menu items
CREATE TABLE `ev_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `alias` varchar(255) NOT NULL,
  `link` text NOT NULL,
  `menutype` varchar(24) NOT NULL,
  `published` tinyint(4) NOT NULL DEFAULT '0',
  -- ... additional fields
  PRIMARY KEY (`id`)
);
```

#### **Categories Table (`ev_categories`)**
```sql
-- Table structure for content categories
CREATE TABLE `ev_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `alias` varchar(255) NOT NULL,
  `extension` varchar(50) NOT NULL DEFAULT 'com_content',
  `published` tinyint(1) NOT NULL DEFAULT '0',
  -- ... additional fields
  PRIMARY KEY (`id`)
);
```

#### **SP Page Builder Table (`ev_sppagebuilder`)**
```sql
-- Table structure for SP Page Builder pages
CREATE TABLE `ev_sppagebuilder` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL DEFAULT '',
  `text` mediumtext NOT NULL,
  `extension` varchar(255) NOT NULL DEFAULT 'com_sppagebuilder',
  `extension_view` varchar(255) NOT NULL DEFAULT 'page',
  `view_id` bigint(20) NOT NULL DEFAULT '0',
  `published` tinyint(3) NOT NULL DEFAULT '1',
  -- ... additional fields
  PRIMARY KEY (`id`)
);
```

#### **Content Articles Table (`ev_content`)**
```sql
-- Standard Joomla content table (articles stored here)
CREATE TABLE `ev_content` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL DEFAULT '',
  `alias` varchar(255) NOT NULL DEFAULT '',
  `catid` int(10) unsigned NOT NULL DEFAULT '0',
  `state` tinyint(3) NOT NULL DEFAULT '0',
  -- ... additional fields
  PRIMARY KEY (`id`)
);
```

## �🔍 Current Page Template Analysis

### 1. `/blog` - Blog Home Page

**Database Location:**
```sql
-- Menu Item (ev_menu)
id: 125
title: "Blog"
alias: "blog"
link: "index.php?option=com_content&view=category&layout=blog&id=25"
menutype: "hidden-menu"
published: 1

-- Category (ev_categories)
id: 25
title: "Blog"
alias: "blog"
extension: "com_content"
published: 1
```

**Current Implementation:**
- **Template Type**: Category Blog Layout (com_content) - **NOT SP Page Builder as previously thought**
- **Menu Configuration**: Hidden menu item pointing to category blog
- **Category ID**: 25 (Blog category)
- **URL Routing**: Handled in `templates/zenbase/html/mod_menu/default_item.php` (lines 247-248)
- **Component**: `com_content` with category blog layout

**Template Files:**
```
templates/zenbase/html/mod_menu/default_item.php  # URL routing for /blog
templates/zenbase/html/com_sppagebuilder/         # SP Page Builder templates
```

**Current Status**: 🔄 **Needs SP Page Builder conversion** (currently uses category blog, not SP Page Builder)

---

### 2. `/knowledge-centre` - Knowledge Centre Category Blog

**Database Location:**
```sql
-- Menu Item (ev_menu)
id: 131
title: "Knowledge Centre"
alias: "knowledge-centre"
link: "index.php?option=com_content&view=category&layout=blog&id=27"
menutype: "mainmenu"
published: 1

-- Category (ev_categories)
id: 27
title: "Knowledge Centre"
alias: "knowledge-centre"
extension: "com_content"
published: 1
```

**Current Implementation:**
- **Template Type**: Category Blog Layout (com_content)
- **Component**: `com_content` with category blog layout
- **Category ID**: 27 ("Knowledge Centre" category)
- **Menu ID**: 131
- **Articles**: All knowledge centre articles under single category

**Template Files:**
```
templates/zenbase/html/com_content/category/blog.php      # Main category blog template
templates/zenbase/html/com_content/category/blog_item.php # Individual article cards
templates/zenbase/html/com_content/category/blog_links.php # Article links
```

**Key Features:**
- Uses `ZenUtil::renderModules('blogRight')` for sidebar content
- Uses `ZenUtil::renderModules('zenBlogTop')` for top content
- Schema.org Blog markup for SEO
- Leading articles in 2-column layout
- Intro articles in responsive grid layout
- Pagination support

**Current Status**: 🔄 **Can be enhanced with SP Page Builder**

---

### 3. `/knowledge-centre/evertrek-difficulty-ratings` - Individual Articles

**Current Implementation:**
- **Template Type**: Single Article View (com_content)
- **Component**: `com_content` with article view
- **Template Used**: Joomla Article with zenbase overrides

**Template Files:**
```
templates/zenbase/html/com_content/article/default.php    # Main article template
```

**Key Features:**
- Schema.org Article markup for SEO
- Full-width article layout with `.zen-bg-light` background
- Responsive article image handling
- Article body in container with white background
- Tag support (currently hidden with `d-none` class)
- Breadcrumb and navigation support

**Current Status**: ✅ **Works well as-is, no changes needed**

---

### 4. `/information-search?searchword=nepal&searchphrase=all` - Search Results

**Database Location:**
```sql
-- Menu Item (ev_menu)
id: 138
title: "Information Search"
alias: "information-search"
link: "index.php?option=com_search&view=search"
menutype: "hidden-menu"
published: 1

-- Note: Uses com_search (Basic Search), not com_finder (Smart Search)
-- Smart Search menu item also exists:
id: 553
title: "smart-search"
alias: "smart-search"
link: "index.php?option=com_finder&view=search"
menutype: "hidden-menu"
published: 1
```

**Current Implementation:**
- **Template Type**: Basic Search Results (com_search) - **NOT Smart Search as previously thought**
- **Component**: `com_search` (Joomla Basic Search)
- **Template Used**: Basic Search with zenbase overrides

**Template Files:**
```
templates/zenbase/html/com_finder/search/default.php         # Main search template
templates/zenbase/html/com_finder/search/default_form.php    # Search form
templates/zenbase/html/com_finder/search/default_results.php # Results listing
templates/zenbase/html/com_finder/search/default_result.php  # Individual result
```

**Key Features:**
- Container-based responsive layout
- Search form with chosen.js integration
- Results highlighting with `behavior.highlighter`
- Pagination support for large result sets
- No results messaging and suggestions

**Current Status**: ✅ **Functional search system, can complement our AJAX filtering**

---

### 5. Related SP Page Builder Components

**Database Location:**
```sql
-- SP Page Builder Pages (ev_sppagebuilder)
-- Blog-related modules and pages:

id: 199
title: "Blog Template Page"
extension: "com_sppagebuilder"
extension_view: "page"
view_id: 0
published: 1

-- Blog-related modules:
id: 20  | title: "Blog Slider - SPPB"              | extension: "mod_sppagebuilder" | module: 116
id: 21  | title: "Knowledge Centre Slider - SPPB"  | extension: "mod_sppagebuilder" | module: 117
id: 23  | title: "Blog Navigation - SPPB"          | extension: "mod_sppagebuilder" | module: 122
id: 24  | title: "Knowledge Centre Navigation"     | extension: "mod_sppagebuilder" | module: 123
id: 33  | title: "Blog Header - SPPB"              | extension: "mod_sppagebuilder" | module: 131
id: 34  | title: "Knowledge Centre Header - SPPB"  | extension: "mod_sppagebuilder" | module: 132

-- Author modules:
id: 164 | title: "SPPB - Blog Author - Lauren"     | extension: "mod_sppagebuilder" | module: 144
id: 165 | title: "SPPB - Blog Author - Zach"       | extension: "mod_sppagebuilder" | module: 145
id: 166 | title: "SPPB - Blog Author - Rosie"      | extension: "mod_sppagebuilder" | module: 146
id: 167 | title: "SPPB - Blog Author - Dave"       | extension: "mod_sppagebuilder" | module: 147
id: 225 | title: "SPPB - Blog Author - Jess"       | extension: "mod_sppagebuilder" | module: 162
id: 251 | title: "SPPB - Blog Author - Sophie"     | extension: "mod_sppagebuilder" | module: 164
```

**Current Implementation:**
- **Blog Template Page**: Existing SP Page Builder page (ID: 199) - could be enhanced
- **Blog Modules**: Multiple SP Page Builder modules for headers, navigation, sliders
- **Author Modules**: Individual author profile modules for blog posts
- **Integration Ready**: Infrastructure exists for SP Page Builder blog system

### **6. Existing Sections & Templates Infrastructure**

**Database Location:**
```sql
-- Sections Library (ev_sppagebuilder_sections)
-- 24 existing reusable sections including:

Blog/Knowledge Centre Specific:
ID 11: "Podcast Article Template"
ID 12: "Knowledge Centre - Three Column Template"
ID 13: "Knowledge Centre - Accordion Template"
ID 15: "Blog Template - Left + Right Images"
ID 18: "Blog List - Navigation Section"
ID 19: "Knowledge Hero"
ID 26: "Blog / Knowledge Center Body Section"
ID 27: "Blog Author Module"

-- Addons Library (ev_sppagebuilder_addons)
-- 17 saved addons including:
ID 2: "Articles Basic Setup"
ID 5: "Blog Card"
ID 6: "Podcast Card"
ID 8-15: Author intro addons (Andy, Dave, Jen, Rosie, Lauren, Zach, Jody, Vicky)
ID 16: "Trip Button CTA"
```

**Current Implementation:**
- **Extensive section library** with blog-specific templates
- **Author profile addons** for individual team members
- **Content-specific addons** for articles and cards
- **Reusable components** for CTAs and navigation
- **Template system** ready for enhancement

**Enhancement Opportunity:**
- ✅ **Leverage existing sections** for quick article scaffolding
- ✅ **Update sections** with our new Blog Grid Cards addon
- ✅ **Create article templates** using section combinations
- ✅ **Author efficiency** through drag-and-drop section library
- ✅ **Consistent branding** with standardized components

**See `docs/sppagebuilder-sections-templates-guide.md` for detailed implementation strategy.**

## 📊 Database Summary & Key Findings

### **Important Discovery: Blog Page Structure**
The database analysis reveals that **both `/blog` and `/knowledge-centre` currently use category blog layouts**, not SP Page Builder pages as initially assumed:

- **`/blog`** → Category ID 25 (Blog category) via menu ID 125
- **`/knowledge-centre`** → Category ID 27 (Knowledge Centre category) via menu ID 131

### **Database Prefix & Structure**
- **Database Name**: `db` (via DDEV)
- **Table Prefix**: `ev_` (not the standard `jos_`)
- **Key Tables**:
  - `ev_menu` - Menu items and navigation
  - `ev_categories` - Content categories (Blog: 25, Knowledge Centre: 27)
  - `ev_content` - Article content (referenced by category)
  - `ev_sppagebuilder` - SP Page Builder pages and modules

### **Existing SP Page Builder Infrastructure**
- **Blog Template Page** exists (ID: 199) but may not be actively used
- **Multiple blog-related modules** already created (sliders, headers, navigation)
- **Author profile modules** for blog posts
- **Ready for integration** with our new blog system

### **Search Functionality**
- **Information Search** uses `com_search` (Basic Search), not Smart Search
- **Smart Search** is available but on separate menu item
- **Both systems** can complement our AJAX filtering

### **Integration Implications**
1. **Articles can be built with SP Page Builder** - Individual articles can use SP Page Builder editor
2. **Category blog layouts** can remain as-is while articles use SP Page Builder content
3. **Hybrid approach possible** - Keep existing category blog structure, enhance individual articles
4. **Existing SP Page Builder infrastructure** can be leveraged
5. **URL structure** can be maintained during conversion
6. **SEO impact** minimal if using SP Page Builder for article content only

## 🎯 Integration Strategies

### Strategy 1: Enhance Existing Pages (Recommended)

#### **For `/knowledge-centre`:**
```
Option A: Replace Category Blog with SP Page Builder Page
Current: Category Blog Layout (com_content)
    ↓
Enhanced: SP Page Builder Page with Blog Grid Cards Addon
    ↓
Benefits: Integrated AJAX filtering, Better UX, Custom layouts

Option B: Keep Category Blog + Enhance Articles (EASIER)
Current: Category Blog Layout (com_content) + Standard Articles
    ↓
Enhanced: Category Blog Layout + SP Page Builder Articles
    ↓
Benefits: Minimal disruption, Enhanced article content, Gradual migration
```

**Implementation Steps:**
1. **Backup** existing menu item configuration
2. **Create** new SP Page Builder page using "Blog with Search Sidebar" template
3. **Configure** Blog Grid Cards addon to show "Knowledge Centre" category
4. **Update** menu item to point to new SP Page Builder page
5. **Test** functionality and SEO impact

#### **For `/blog`:**
```
Current: Alias → SP Page Builder Page (basic)
    ↓
Enhanced: SP Page Builder Page with Blog Grid Cards Addon
    ↓
Benefits:
- Professional blog layout
- Integrated filtering
- Multiple layout options
- Easy content management
```

**Implementation Steps:**
1. **Create** new SP Page Builder page using "Full Width Blog Grid" template
2. **Configure** for broader article categories or all published articles
3. **Update** existing alias menu item target
4. **Test** and refine layout

### Strategy 2: Hybrid Template + SP Page Builder Content (RECOMMENDED)

#### **Enhanced Article Strategy:**
```
Template-Driven Page Structure + SP Page Builder Content:

┌─────────────────────────────────────┐
│ TEMPLATE CONTROLLED (Dynamic)       │
├─────────────────────────────────────┤
│ • Header/Navigation                 │
│ • Article Title (from Joomla field) │
│ • Author Info (from user profile)   │
│ • Publication Date (automatic)      │
│ • Category/Tags (from Joomla)       │
│ • Breadcrumbs (automatic)           │
├─────────────────────────────────────┤
│ SP PAGE BUILDER CONTENT AREA        │
├─────────────────────────────────────┤
│ • Rich content sections             │
│ • Images and galleries              │
│ • Custom layouts                    │
│ • Interactive elements              │
│ • Embedded media                    │
├─────────────────────────────────────┤
│ TEMPLATE CONTROLLED (Dynamic)       │
├─────────────────────────────────────┤
│ • Related Articles (automatic)      │
│ • Social Sharing (template)         │
│ • Comments Section (if enabled)     │
│ • Footer/Navigation                 │
└─────────────────────────────────────┘
```

#### **Benefits:**
- ✅ **Authors focus only on content** - no layout configuration needed
- ✅ **Consistent page structure** across all articles
- ✅ **Dynamic data population** from Joomla fields
- ✅ **Rich content capabilities** where it matters most
- ✅ **Minimal author training** required
- ✅ **Template updates** affect all articles automatically

**See `docs/hybrid-sppb-article-implementation.md` for detailed technical implementation.**

### Strategy 3: Create New Dedicated Pages

#### **New Page Structure:**
```
/blog                    # Main blog home (Full Width Blog Grid)
/knowledge-centre        # Keep existing for SEO continuity
/articles               # Comprehensive filterable page (Blog with Search Sidebar)
/guides                 # Specific guide categories (Blog with Featured Section)
/resources              # Resource-focused content (Minimal Blog Layout)
```

#### **Benefits:**
- ✅ **Preserves existing URLs** for SEO
- ✅ **Adds enhanced functionality** without disruption
- ✅ **Provides multiple entry points** for different user needs
- ✅ **Allows gradual migration** and testing

### Strategy 4: Full SP Page Builder Migration (Advanced)

#### **Implementation Plan:**
1. **Phase 1**: Create `/articles` page with full filtering capabilities
2. **Phase 2**: Enhance `/blog` with SP Page Builder integration
3. **Phase 3**: Gradually migrate `/knowledge-centre` users to new system
4. **Phase 4**: Eventually replace or redirect old pages

## 🚀 Recommended Implementation Plan

### **NEW RECOMMENDED APPROACH: SP Page Builder Articles**

### Phase 1: Hybrid Template Development (Week 1)
- [ ] **Create article template override** with SP Page Builder integration
- [ ] **Develop helper functions** for dynamic data (author, related articles, etc.)
- [ ] **Set up custom fields** for enhanced author profiles
- [ ] **Test template** with existing articles
- [ ] **Validate dynamic data population** and SP Page Builder rendering

### Phase 1.5: Sections & Templates Enhancement (Week 1-2)
- [ ] **Audit existing sections** (24 available) for blog enhancement opportunities
- [ ] **Update blog-specific sections** (IDs: 11, 12, 13, 15, 26, 27) with new addons
- [ ] **Create article templates** using section combinations
- [ ] **Test section library** for drag-and-drop functionality
- [ ] **Document author workflow** for efficient content creation

### Phase 2: Content Templates & Article Migration (Week 2-3)
- [ ] **Create SP Page Builder content templates** for common article types
- [ ] **Convert 2-3 high-priority articles** to hybrid approach
- [ ] **Test author workflow** with template + SP Page Builder content
- [ ] **Validate template-driven elements** (author bio, related articles, etc.)
- [ ] **Refine templates** based on author feedback

### Phase 3: Optional Blog Page Enhancement (Week 4)
- [ ] **Create test SP Page Builder page** using "Blog with Search Sidebar" template
- [ ] **Configure** to display enhanced SP Page Builder articles
- [ ] **Test integrated filtering** functionality
- [ ] **Compare with existing category blog** approach
- [ ] **Decide on migration** based on results

### Phase 4: Author Efficiency System (Week 4-5)
- [ ] **Create content template selection interface** for SP Page Builder section
- [ ] **Implement quick-start content templates** (Travel Guide, How-To, News, etc.)
- [ ] **Train content authors** on hybrid workflow (Joomla fields + SP Page Builder content)
- [ ] **Create author documentation** for template usage
- [ ] **Set up author profile management** system

### Phase 5: Full Integration (Week 5+)
- [ ] **Create additional SP Page Builder pages** if needed
- [ ] **Set up cross-linking** between enhanced articles
- [ ] **Update navigation** to highlight enhanced content
- [ ] **Monitor SEO impact** and user metrics
- [ ] **Gather author feedback** and refine templates

## 📊 Template Compatibility Matrix

| Page Type | Current Template | Database Location | SP Page Builder Compatible | Integration Effort | SEO Impact |
|-----------|------------------|-------------------|---------------------------|-------------------|------------|
| `/blog` | **Category Blog** | Menu ID: 125, Category ID: 25 | ✅ **Compatible** | � **Medium** | � **Moderate** |
| `/knowledge-centre` | **Category Blog** | Menu ID: 131, Category ID: 27 | ✅ **Compatible** | 🟡 **Medium** | 🟡 **Moderate** |
| **Individual Articles** | **Article View** | Content in `ev_content` table | ✅ **SP Page Builder Ready** | 🟢 **Low** | 🟢 **Minimal** |
| Search Results | **Basic Search** | Menu ID: 138 (com_search) | ✅ **Complementary** | 🟢 **None** | 🟢 **None** |

### **UPDATED: SP Page Builder Articles Discovery**
- **Individual articles can use SP Page Builder** - This changes everything!
- **Recommended approach**: Keep existing category blog layouts, enhance articles with SP Page Builder
- **Integration effort**: Much lower - gradual article-by-article enhancement
- **SEO impact**: Minimal - URL structure and blog layouts remain unchanged
- **Best of both worlds**: Familiar blog structure + rich SP Page Builder article content

### **MAJOR ADVANTAGE: Existing Sections & Templates Infrastructure**
- **24 existing sections** including 8 blog/knowledge centre specific templates
- **17 saved addons** including author profiles and content blocks
- **Template system** ready for enhancement with article scaffolding
- **Author efficiency** through drag-and-drop section library and quick-start templates
- **Consistent branding** with standardized, reusable components

**This hybrid approach means authors can:**
- ✅ **Focus only on content creation** - no page layout configuration needed
- ✅ **Use rich SP Page Builder features** for the content section only
- ✅ **Automatic template-driven elements** (headers, author info, related articles)
- ✅ **Consistent branding** across all articles without manual setup
- ✅ **Standard Joomla workflow** for metadata and article management
- ✅ **Dynamic data population** from user profiles and article fields

## 🔧 Technical Considerations

### Database Impact
- **No database changes** required for existing articles
- **Articles remain** in `#__content` table
- **Categories and tags** work as-is
- **SEF URLs** maintained through proper menu configuration

### Performance Considerations
- **AJAX filtering** reduces page load times
- **Integrated approach** eliminates separate component overhead
- **Caching compatibility** with Joomla's cache system
- **Mobile optimization** built into responsive design

### SEO Preservation
- **URL structure** can be maintained
- **Schema markup** preserved and enhanced
- **Meta tags** handled by SP Page Builder
- **Breadcrumbs** continue to work
- **Sitemap integration** remains functional

## 📋 Testing Checklist

### Functionality Testing
- [ ] **Article display** works correctly
- [ ] **Filtering** functions properly (search, categories, tags)
- [ ] **AJAX requests** complete successfully
- [ ] **Pagination** works if implemented
- [ ] **Responsive design** functions across devices

### SEO Testing
- [ ] **Meta tags** are properly set
- [ ] **Schema markup** is present and valid
- [ ] **URL structure** is maintained
- [ ] **Breadcrumbs** display correctly
- [ ] **Search engine indexing** is not affected

### Performance Testing
- [ ] **Page load times** are acceptable
- [ ] **AJAX response times** are under 1 second
- [ ] **Database queries** are optimized
- [ ] **Caching** works properly
- [ ] **Mobile performance** is satisfactory

## 🎉 Expected Benefits

### User Experience
- ✅ **Faster article discovery** through integrated filtering
- ✅ **Better mobile experience** with responsive design
- ✅ **Consistent interface** across all blog pages
- ✅ **Professional appearance** with modern card layouts

### Content Management
- ✅ **Easier page creation** through SP Page Builder templates
- ✅ **Flexible layouts** for different content types
- ✅ **No additional content migration** required
- ✅ **Simplified maintenance** with integrated system

### Technical Benefits
- ✅ **Reduced complexity** with single addon approach
- ✅ **Better performance** with optimized AJAX
- ✅ **Enhanced SEO** with proper schema markup
- ✅ **Future-proof architecture** with modern technologies

This integration strategy provides a clear path to enhance the existing blog and knowledge centre functionality while preserving SEO value and maintaining user experience continuity.
