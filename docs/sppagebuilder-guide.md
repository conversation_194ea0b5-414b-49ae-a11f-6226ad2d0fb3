# SP Page Builder Implementation Guide

## Table of Contents
1. [Left-Sidebar Search Filter Implementation](#search-filter)
2. [Blog Articles as Grid Layout Cards](#grid-layout)
3. [Creating Custom Page Templates](#page-templates)

---

## Integrated Blog Filter Implementation {#search-filter}

### Current SP Page Builder Setup
- SP Page Builder is installed with custom addons in `templates/zenbase/sppagebuilder/addons/`
- Existing custom addons include `holidaytabs`, `holidaygrid`, and `articles`
- **NEW**: Blog Grid Cards addon now includes integrated filtering

### Integrated Approach (Recommended)

The AJAX blog filter is now **built into the Blog Grid Cards addon** as a standard feature. This eliminates the need for separate filter addons and provides seamless integration.

**Key Benefits:**
- ✅ **Single addon** handles both grid and filtering
- ✅ **No complex setup** or ID matching required
- ✅ **Three filter styles**: Compact, Expanded, Minimal
- ✅ **AJAX-powered** filtering without page reloads
- ✅ **Mobile-optimized** responsive design

**Filter Styles Available:**

1. **Compact Horizontal** - All filters in one row, perfect for full-width layouts
2. **Expanded Vertical** - Filters stacked vertically, ideal for sidebar layouts
3. **Minimal Search** - Just search box, great for simple blogs

**2. Admin Configuration** (`admin.php`):
```php
<?php
defined('_JEXEC') or die('Restricted access');

SpAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'blog_search_filter',
        'title' => JText::_('Blog Search Filter'),
        'desc' => JText::_('Advanced search filter for blog articles'),
        'category' => 'Content',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => JText::_('COM_SPPAGEBUILDER_ADDON_ADMIN_LABEL'),
                    'desc' => JText::_('COM_SPPAGEBUILDER_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => JText::_('Filter Title'),
                    'desc' => JText::_('Enter title for the search filter'),
                    'std' => 'Search Articles'
                ),
                'show_search_box' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Search Box'),
                    'desc' => JText::_('Display text search input'),
                    'std' => 1
                ),
                'show_categories' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Categories Filter'),
                    'desc' => JText::_('Display category checkboxes'),
                    'std' => 1
                ),
                'show_tags' => array(
                    'type' => 'checkbox',
                    'title' => JText::_('Show Tags Filter'),
                    'desc' => JText::_('Display tag checkboxes'),
                    'std' => 1
                ),
                'target_results_id' => array(
                    'type' => 'text',
                    'title' => JText::_('Target Results Container ID'),
                    'desc' => JText::_('CSS ID of the container to update with results'),
                    'std' => 'blog-results'
                )
            )
        )
    )
);
```

**3. Frontend Rendering** (`site.php`):
```php
<?php
defined('_JEXEC') or die('Restricted access');

class SppagebuilderAddonBlog_search_filter extends SppagebuilderAddons
{
    public function render()
    {
        $settings = $this->addon->settings;
        $class = (isset($settings->class) && $settings->class) ? $settings->class : '';
        
        // Get categories
        $categories = $this->getCategories();
        
        // Get tags
        $tags = $this->getTags();
        
        $output = '<div class="sppb-addon sppb-addon-blog-search-filter ' . $class . '">';
        
        if (isset($settings->title) && $settings->title) {
            $output .= '<h3 class="sppb-addon-title">' . $settings->title . '</h3>';
        }
        
        $output .= '<div class="sppb-addon-content">';
        $output .= '<form class="blog-search-filter-form" data-target="' . $settings->target_results_id . '">';
        
        // Search box
        if ($settings->show_search_box) {
            $output .= '<div class="filter-group">';
            $output .= '<label>Search:</label>';
            $output .= '<input type="text" name="search" class="form-control" placeholder="Enter keywords...">';
            $output .= '</div>';
        }
        
        // Categories
        if ($settings->show_categories && !empty($categories)) {
            $output .= '<div class="filter-group">';
            $output .= '<label>Categories:</label>';
            foreach ($categories as $category) {
                $output .= '<div class="checkbox">';
                $output .= '<label><input type="checkbox" name="categories[]" value="' . $category->id . '"> ' . $category->title . '</label>';
                $output .= '</div>';
            }
            $output .= '</div>';
        }
        
        // Tags
        if ($settings->show_tags && !empty($tags)) {
            $output .= '<div class="filter-group">';
            $output .= '<label>Tags:</label>';
            foreach ($tags as $tag) {
                $output .= '<div class="checkbox">';
                $output .= '<label><input type="checkbox" name="tags[]" value="' . $tag->id . '"> ' . $tag->title . '</label>';
                $output .= '</div>';
            }
            $output .= '</div>';
        }
        
        $output .= '<button type="submit" class="btn btn-primary">Filter Results</button>';
        $output .= '<button type="reset" class="btn btn-secondary">Clear Filters</button>';
        $output .= '</form>';
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
    
    private function getCategories()
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true)
            ->select('id, title, alias')
            ->from('#__categories')
            ->where('extension = ' . $db->quote('com_content'))
            ->where('published = 1')
            ->order('title ASC');
        
        return $db->setQuery($query)->loadObjectList();
    }
    
    private function getTags()
    {
        $db = JFactory::getDbo();
        $query = $db->getQuery(true)
            ->select('id, title, alias')
            ->from('#__tags')
            ->where('published = 1')
            ->order('title ASC');
        
        return $db->setQuery($query)->loadObjectList();
    }
}
```

**4. JavaScript Functionality** (`assets/js/filter.js`):
```javascript
jQuery(document).ready(function($) {
    $('.blog-search-filter-form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var targetId = form.data('target');
        var formData = form.serialize();
        
        // Show loading state
        $('#' + targetId).addClass('loading');
        
        $.ajax({
            url: 'index.php?option=com_ajax&plugin=blog_filter&format=json',
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    $('#' + targetId).html(response.data);
                }
                $('#' + targetId).removeClass('loading');
            },
            error: function() {
                $('#' + targetId).removeClass('loading');
                alert('Error loading results');
            }
        });
    });
    
    // Clear filters
    $('.blog-search-filter-form button[type="reset"]').on('click', function() {
        var form = $(this).closest('form');
        var targetId = form.data('target');
        
        // Reset form and reload all articles
        form[0].reset();
        form.trigger('submit');
    });
});
```

#### Option 2: Modify Existing Articles Addon

**Extend the existing articles addon** in `templates/zenbase/sppagebuilder/addons/articles/` to include search functionality:

1. **Add search parameters to `admin.php`**:
```php
'enable_search_filter' => array(
    'type' => 'checkbox',
    'title' => JText::_('Enable Search Filter'),
    'desc' => JText::_('Show search filter above articles'),
    'std' => 0
),
'search_position' => array(
    'type' => 'select',
    'title' => JText::_('Search Filter Position'),
    'values' => array(
        'top' => 'Above Articles',
        'left' => 'Left Sidebar',
        'right' => 'Right Sidebar'
    ),
    'std' => 'top',
    'depends' => array('enable_search_filter' => '1')
)
```

2. **Modify `site.php` to include search form** when enabled.

---

## Blog Articles as Grid Layout Cards {#grid-layout}

### Current Articles Addon Capabilities
- Supports grid layout with configurable columns
- Has options to show/hide intro text, thumbnails, author, date
- Supports different post types and filtering

### Customizing for Card Layout Without Body Text

#### Option 1: Template Override (Recommended)

**1. Override Articles Addon Template**:
```php
// File: templates/zenbase/sppagebuilder/addons/articles/site.php (modify existing)

// In the render() method, modify the article output section:
foreach ($items as $key => $item) {
    $output .= '<div class="sppb-col-sm-' . round(12 / $columns) . '">';
    $output .= '<div class="sppb-addon-article sppb-article-card">';
    
    // Article image
    if (!$hide_thumbnail && isset($item->image_medium) && $item->image_medium) {
        $output .= '<div class="sppb-article-image">';
        $output .= '<a href="' . $item->link . '">';
        $output .= '<img class="sppb-img-responsive" src="' . $item->image_medium . '" alt="' . $item->title . '">';
        $output .= '</a>';
        $output .= '</div>';
    }
    
    $output .= '<div class="sppb-article-content">';
    
    // Title
    $output .= '<h3 class="sppb-article-title">';
    $output .= '<a href="' . $item->link . '">' . $item->title . '</a>';
    $output .= '</h3>';
    
    // Meta information
    $output .= '<div class="sppb-article-meta">';
    if ($show_author) {
        $output .= '<span class="sppb-article-author">' . $item->username . '</span>';
    }
    if ($show_date) {
        $output .= '<span class="sppb-article-date">' . JHtml::_('date', $item->created, 'M d, Y') . '</span>';
    }
    $output .= '</div>';
    
    // Remove intro text section completely
    // if ($show_intro && $item->introtext) { ... } // Comment out or remove
    
    // Read more button (optional)
    if ($show_readmore) {
        $output .= '<div class="sppb-article-readmore">';
        $output .= '<a href="' . $item->link . '" class="btn btn-primary">' . $readmore_text . '</a>';
        $output .= '</div>';
    }
    
    $output .= '</div>'; // Close content
    $output .= '</div>'; // Close article
    $output .= '</div>'; // Close column
}
```

#### Option 2: Custom CSS to Hide Body Text

**Add to your theme's CSS**:
```css
/* File: templates/zenbase/css/custom.css */

/* Hide intro text in article cards */
.sppb-addon-articles .sppb-article-introtext {
    display: none !important;
}

/* Enhanced card styling */
.sppb-addon-articles .sppb-addon-article {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.sppb-addon-articles .sppb-addon-article:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.sppb-addon-articles .sppb-article-image {
    flex-shrink: 0;
}

.sppb-addon-articles .sppb-article-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.sppb-addon-articles .sppb-article-content {
    padding: 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.sppb-addon-articles .sppb-article-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    flex-grow: 1;
}

.sppb-addon-articles .sppb-article-meta {
    margin-top: auto;
    font-size: 0.9rem;
    color: #666;
    border-top: 1px solid #f0f0f0;
    padding-top: 0.5rem;
}

.sppb-addon-articles .sppb-article-readmore {
    margin-top: 1rem;
}
```

#### Option 3: Create Custom Blog Cards Addon

**Create a new addon specifically for blog cards**:
```
templates/zenbase/sppagebuilder/addons/blog_cards/
├── admin.php
├── site.php
└── assets/
    └── css/blog-cards.css
```

This would give you complete control over the card layout and functionality.

---

## Creating Custom Page Templates {#page-templates}

### Current SP Page Builder Template System
- SP Page Builder supports page templates and duplication
- Templates are stored as JSON in the `#__sppagebuilder` table
- Built-in template library available from JoomShaper
- Save2Copy functionality for duplicating pages

### Implementation Options

#### Option 1: Pre-built Page Templates (Recommended)

**1. Create Template Pages**:
```php
// Create a custom controller method to generate template pages
// File: administrator/components/com_sppagebuilder/controllers/templates.php

class SppagebuilderControllerTemplates extends JControllerLegacy
{
    public function createBlogTemplate()
    {
        $model = $this->getModel('Page');
        
        // Define template structure
        $templateData = json_encode([
            [
                'type' => 'section',
                'settings' => [
                    'fullscreen' => 0,
                    'background_type' => 'color',
                    'background_color' => '#ffffff'
                ],
                'columns' => [
                    [
                        'settings' => ['md' => 3],
                        'addons' => [
                            [
                                'name' => 'blog_search_filter',
                                'settings' => [
                                    'title' => 'Filter Articles',
                                    'show_search_box' => 1,
                                    'show_categories' => 1,
                                    'target_results_id' => 'blog-results'
                                ]
                            ]
                        ]
                    ],
                    [
                        'settings' => ['md' => 9],
                        'addons' => [
                            [
                                'name' => 'sp_articles',
                                'settings' => [
                                    'title' => 'Latest Articles',
                                    'limit' => 12,
                                    'columns' => 3,
                                    'show_intro' => 0,
                                    'show_readmore' => 1,
                                    'class' => 'blog-results'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]);
        
        $templateId = $model->createBrandNewPage(
            'Blog Template with Search Filter',
            'com_content',
            'article',
            0
        );
        
        // Update with template data
        $db = JFactory::getDbo();
        $query = $db->getQuery(true)
            ->update('#__sppagebuilder')
            ->set('text = ' . $db->quote($templateData))
            ->where('id = ' . (int) $templateId);
        
        $db->setQuery($query)->execute();
        
        return $templateId;
    }
}
```

**2. Template Management Interface**:
```php
// Add to SP Page Builder admin menu
// File: administrator/components/com_sppagebuilder/views/templates/

class SppagebuilderViewTemplates extends JViewLegacy
{
    public function display($tpl = null)
    {
        $this->templates = $this->getTemplates();
        parent::display($tpl);
    }
    
    private function getTemplates()
    {
        return [
            [
                'id' => 'blog-with-sidebar',
                'title' => 'Blog with Search Sidebar',
                'description' => 'Blog layout with left sidebar search filter',
                'preview' => 'path/to/preview.jpg'
            ],
            [
                'id' => 'blog-grid-cards',
                'title' => 'Blog Grid Cards',
                'description' => 'Clean grid layout for blog articles',
                'preview' => 'path/to/preview.jpg'
            ]
        ];
    }
}
```

#### Option 2: Template Duplication System

**1. Enhanced Save2Copy Functionality**:
```php
// Extend the existing page model
// File: administrator/components/com_sppagebuilder/models/page.php

public function createFromTemplate($templateId, $newTitle)
{
    $db = $this->getDbo();
    
    // Get template data
    $query = $db->getQuery(true)
        ->select('*')
        ->from('#__sppagebuilder')
        ->where('id = ' . (int) $templateId);
    
    $template = $db->setQuery($query)->loadObject();
    
    if (!$template) {
        return false;
    }
    
    // Create new page
    $newPage = clone $template;
    unset($newPage->id);
    $newPage->title = $newTitle;
    $newPage->created_on = JFactory::getDate()->toSql();
    $newPage->created_by = JFactory::getUser()->id;
    
    return $db->insertObject('#__sppagebuilder', $newPage);
}
```

**2. Template Library Interface**:
```html
<!-- File: administrator/components/com_sppagebuilder/views/templates/tmpl/default.php -->
<div class="template-library">
    <h2>Page Templates</h2>
    
    <div class="template-grid">
        <?php foreach ($this->templates as $template): ?>
        <div class="template-card">
            <div class="template-preview">
                <img src="<?php echo $template['preview']; ?>" alt="<?php echo $template['title']; ?>">
            </div>
            <div class="template-info">
                <h3><?php echo $template['title']; ?></h3>
                <p><?php echo $template['description']; ?></p>
                <button class="btn btn-primary" onclick="createFromTemplate('<?php echo $template['id']; ?>')">
                    Use Template
                </button>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<script>
function createFromTemplate(templateId) {
    var title = prompt('Enter page title:');
    if (title) {
        window.location.href = 'index.php?option=com_sppagebuilder&task=templates.createFromTemplate&template_id=' + templateId + '&title=' + encodeURIComponent(title);
    }
}
</script>
```

#### Option 3: JSON Template Import/Export

**1. Template Export Function**:
```php
public function exportTemplate()
{
    $input = JFactory::getApplication()->input;
    $pageId = $input->getInt('id');
    
    $db = JFactory::getDbo();
    $query = $db->getQuery(true)
        ->select('title, text')
        ->from('#__sppagebuilder')
        ->where('id = ' . $pageId);
    
    $page = $db->setQuery($query)->loadObject();
    
    $template = [
        'name' => $page->title,
        'version' => '1.0',
        'data' => json_decode($page->text),
        'created' => date('Y-m-d H:i:s')
    ];
    
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $page->title . '.json"');
    echo json_encode($template, JSON_PRETTY_PRINT);
    exit;
}
```

**2. Template Import Function**:
```php
public function importTemplate()
{
    $input = JFactory::getApplication()->input;
    $files = $input->files->get('template_file');
    
    if ($files['error'] === 0) {
        $templateData = file_get_contents($files['tmp_name']);
        $template = json_decode($templateData, true);
        
        if ($template && isset($template['data'])) {
            $pageId = $this->createBrandNewPage($template['name']);
            
            $db = JFactory::getDbo();
            $query = $db->getQuery(true)
                ->update('#__sppagebuilder')
                ->set('text = ' . $db->quote(json_encode($template['data'])))
                ->where('id = ' . $pageId);
            
            $db->setQuery($query)->execute();
            
            return $pageId;
        }
    }
    
    return false;
}
```

### Usage Instructions

1. **For Editors Creating New Pages**:
   - Go to SP Page Builder → Pages → New
   - Select from available templates
   - Customize content as needed
   - Save and publish

2. **Template Management**:
   - Create master templates with common layouts
   - Use Save2Copy to duplicate successful pages
   - Export/import templates between sites
   - Organize templates by category (Blog, Landing Page, etc.)

3. **Global Blocks**:
   - Create reusable sections (headers, footers, contact forms)
   - Save as templates for consistent branding
   - Update once, apply everywhere

---

## Next Steps

1. **Implement Search Filter**: Choose between custom addon or extending existing articles addon
2. **Customize Grid Layout**: Apply card styling and remove body text
3. **Create Template System**: Set up page templates for editors
4. **Test and Refine**: Ensure all components work together seamlessly

## Support Resources

- SP Page Builder Documentation: Available in component backend
- Custom Addon Development: Reference existing addons in `/components/com_sppagebuilder/addons/`
- Template Customization: Override files in `/templates/zenbase/sppagebuilder/`
