# Hybrid SP Page Builder Article Implementation

## 🎯 Overview

This guide details the implementation of a **hybrid approach** where SP Page Builder is used **only for the content section** of blog/knowledge centre articles, while all other page elements are handled by **template-driven dynamic data** from standard Joomla fields.

## 🏗️ Architecture Design

### **Page Structure Separation**

```
┌─────────────────────────────────────────────────────────────┐
│                    ARTICLE TEMPLATE                         │
├─────────────────────────────────────────────────────────────┤
│ HEADER SECTION (Template Controlled)                       │
│ • Site Navigation (zenbase template)                       │
│ • Breadcrumbs (automatic from category)                    │
│ • Article Category Badge (from Joomla category)            │
├─────────────────────────────────────────────────────────────┤
│ ARTICLE META (Template Controlled)                         │
│ • Title: <?php echo $this->item->title; ?>                 │
│ • Author: <?php echo $this->item->created_by_alias; ?>     │
│ • Date: <?php echo HTMLHelper::_('date', $this->item->...); │
│ • Tags: <?php echo $this->item->tags->itemTags; ?>         │
│ • Reading Time: (calculated from content)                  │
├─────────────────────────────────────────────────────────────┤
│ CONTENT AREA (SP Page Builder)                             │
│ • Rich content sections                                     │
│ • Custom layouts and components                            │
│ • Images, galleries, media                                 │
│ • Interactive elements                                      │
│ • Call-to-action blocks                                     │
├─────────────────────────────────────────────────────────────┤
│ FOOTER SECTION (Template Controlled)                       │
│ • Author Bio (from user profile + custom fields)          │
│ • Related Articles (automatic based on category/tags)      │
│ • Social Sharing (template component)                      │
│ • Comments (if enabled)                                     │
│ • Newsletter Signup (template component)                   │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Technical Implementation

### **1. Article Template Override**

Create a custom article template that integrates SP Page Builder content:

```php
// File: templates/zenbase/html/com_content/article/default.php

<?php defined('_JEXEC') or die; ?>

<article class="blog-article">
    <!-- TEMPLATE CONTROLLED HEADER -->
    <header class="article-header">
        <div class="article-meta">
            <span class="category-badge"><?php echo $this->item->category_title; ?></span>
            <time datetime="<?php echo HTMLHelper::_('date', $this->item->created, 'c'); ?>">
                <?php echo HTMLHelper::_('date', $this->item->created, 'F j, Y'); ?>
            </time>
        </div>
        
        <h1 class="article-title"><?php echo $this->escape($this->item->title); ?></h1>
        
        <div class="article-byline">
            <div class="author-info">
                <?php if ($this->item->created_by_alias): ?>
                    <span class="author-name"><?php echo $this->item->created_by_alias; ?></span>
                <?php else: ?>
                    <span class="author-name"><?php echo $this->item->author; ?></span>
                <?php endif; ?>
            </div>
            <div class="reading-time">
                <?php echo $this->calculateReadingTime($this->item->text); ?> min read
            </div>
        </div>
    </header>

    <!-- SP PAGE BUILDER CONTENT AREA -->
    <div class="article-content">
        <?php if ($this->isSPPageBuilderArticle($this->item)): ?>
            <!-- Render SP Page Builder content -->
            <?php echo $this->renderSPPageBuilderContent($this->item); ?>
        <?php else: ?>
            <!-- Standard Joomla article content -->
            <?php echo $this->item->text; ?>
        <?php endif; ?>
    </div>

    <!-- TEMPLATE CONTROLLED FOOTER -->
    <footer class="article-footer">
        <!-- Author Bio Section -->
        <?php if ($authorBio = $this->getAuthorBio($this->item->created_by)): ?>
            <div class="author-bio">
                <div class="author-avatar">
                    <img src="<?php echo $authorBio->avatar; ?>" alt="<?php echo $authorBio->name; ?>">
                </div>
                <div class="author-details">
                    <h3><?php echo $authorBio->name; ?></h3>
                    <p><?php echo $authorBio->bio; ?></p>
                    <div class="author-social">
                        <?php echo $this->renderAuthorSocial($authorBio); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Related Articles -->
        <div class="related-articles">
            <h3>Related Articles</h3>
            <?php echo $this->getRelatedArticles($this->item); ?>
        </div>

        <!-- Social Sharing -->
        <div class="social-sharing">
            <?php echo $this->renderSocialSharing($this->item); ?>
        </div>
    </footer>
</article>
```

### **2. SP Page Builder Integration Helper**

```php
// File: templates/zenbase/html/com_content/article/default_helpers.php

class ArticleHelpers
{
    /**
     * Check if article uses SP Page Builder
     */
    public function isSPPageBuilderArticle($item)
    {
        // Check if article has SP Page Builder content
        $db = Factory::getDbo();
        $query = $db->getQuery(true)
            ->select('COUNT(*)')
            ->from('#__sppagebuilder')
            ->where('extension = ' . $db->quote('com_content'))
            ->where('extension_view = ' . $db->quote('article'))
            ->where('view_id = ' . (int) $item->id);
        
        return (bool) $db->setQuery($query)->loadResult();
    }

    /**
     * Render SP Page Builder content for article
     */
    public function renderSPPageBuilderContent($item)
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true)
            ->select('text')
            ->from('#__sppagebuilder')
            ->where('extension = ' . $db->quote('com_content'))
            ->where('extension_view = ' . $db->quote('article'))
            ->where('view_id = ' . (int) $item->id);
        
        $spContent = $db->setQuery($query)->loadResult();
        
        if ($spContent) {
            // Load SP Page Builder parser
            require_once JPATH_COMPONENT_ADMINISTRATOR . '/helpers/parser.php';
            return AddonParser::viewAddons(json_decode($spContent), false, 'article');
        }
        
        return $item->text; // Fallback to standard content
    }

    /**
     * Get author bio from user profile and custom fields
     */
    public function getAuthorBio($userId)
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true)
            ->select('u.name, u.email, p.profile_value as bio')
            ->from('#__users AS u')
            ->leftJoin('#__user_profiles AS p ON u.id = p.user_id AND p.profile_key = ' . $db->quote('profile.bio'))
            ->where('u.id = ' . (int) $userId);
        
        $author = $db->setQuery($query)->loadObject();
        
        if ($author) {
            // Add avatar (could be from custom field or gravatar)
            $author->avatar = $this->getAuthorAvatar($userId);
            return $author;
        }
        
        return null;
    }

    /**
     * Calculate reading time from content
     */
    public function calculateReadingTime($content)
    {
        $wordCount = str_word_count(strip_tags($content));
        $readingTime = ceil($wordCount / 200); // Average reading speed
        return max(1, $readingTime); // Minimum 1 minute
    }

    /**
     * Get related articles based on category and tags
     */
    public function getRelatedArticles($item, $limit = 3)
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true)
            ->select('a.id, a.title, a.alias, a.catid, a.created, a.images')
            ->from('#__content AS a')
            ->where('a.state = 1')
            ->where('a.id != ' . (int) $item->id)
            ->where('a.catid = ' . (int) $item->catid)
            ->order('a.created DESC')
            ->setLimit($limit);
        
        return $db->setQuery($query)->loadObjectList();
    }
}
```

### **3. Article Creation Workflow**

#### **For Authors: <AUTHORS>
```
1. Create New Article (standard Joomla process)
2. Fill in standard fields:
   - Title
   - Category  
   - Tags
   - Author alias (if different)
   - Meta description
3. Switch to "SP Page Builder" tab
4. Create rich content using SP Page Builder
5. Save article
```

#### **Template Handles Automatically:**
- ✅ **Header structure** and navigation
- ✅ **Article metadata** display
- ✅ **Author information** from user profile
- ✅ **Related articles** based on category/tags
- ✅ **Social sharing** buttons
- ✅ **SEO elements** (structured data, meta tags)

### **4. Custom Fields Integration**

Add custom fields for enhanced author information:

```php
// Custom fields for user profiles:
// - Author Bio (textarea)
// - Author Avatar (media)
// - Twitter Handle (text)
// - LinkedIn URL (url)
// - Expertise Areas (checkboxes)

// Access in template:
$customFields = FieldsHelper::getFields('com_users.user', $user, true);
foreach ($customFields as $field) {
    if ($field->name == 'author-bio') {
        $authorBio = $field->rawvalue;
    }
}
```

## 🎨 Content Section Templates

### **SP Page Builder Content Templates**

Create **content-only templates** for common article types:

#### **Travel Guide Content Template:**
```json
[
    {
        "type": "section",
        "settings": {"padding": "40px 0"},
        "columns": [
            {
                "settings": {"md": 8, "offset_md": 2},
                "addons": [
                    {
                        "name": "sp_text_block",
                        "settings": {
                            "title": "Quick Facts",
                            "text": "[Placeholder for key information]"
                        }
                    }
                ]
            }
        ]
    },
    {
        "type": "section", 
        "settings": {"padding": "40px 0"},
        "columns": [
            {
                "settings": {"md": 6},
                "addons": [
                    {
                        "name": "sp_text_block",
                        "settings": {
                            "text": "[Main content area - left column]"
                        }
                    }
                ]
            },
            {
                "settings": {"md": 6},
                "addons": [
                    {
                        "name": "sp_image",
                        "settings": {
                            "image": "[Featured image placeholder]"
                        }
                    }
                ]
            }
        ]
    }
]
```

#### **How-To Guide Content Template:**
```json
[
    {
        "type": "section",
        "columns": [
            {
                "settings": {"md": 12},
                "addons": [
                    {
                        "name": "sp_accordion",
                        "settings": {
                            "title": "Step-by-Step Guide",
                            "items": [
                                {"title": "Step 1", "content": "[Instructions]"},
                                {"title": "Step 2", "content": "[Instructions]"},
                                {"title": "Step 3", "content": "[Instructions]"}
                            ]
                        }
                    }
                ]
            }
        ]
    }
]
```

## 🚀 Implementation Benefits

### **For Authors: <AUTHORS>
- ✅ **Focus only on content** - no layout configuration
- ✅ **Rich content capabilities** with SP Page Builder
- ✅ **Consistent article structure** automatically
- ✅ **Standard Joomla workflow** for metadata
- ✅ **Template-based efficiency** for common elements

### **For Developers:**
- ✅ **Centralized template control** for site-wide updates
- ✅ **Dynamic data integration** from Joomla fields
- ✅ **SEO optimization** handled by template
- ✅ **Performance optimization** through template caching
- ✅ **Maintenance efficiency** - update template, not individual articles

### **For Site Management:**
- ✅ **Brand consistency** across all articles
- ✅ **Easy template updates** affect all content
- ✅ **Author information** managed centrally
- ✅ **Related content** generated automatically
- ✅ **Social features** integrated seamlessly

## 📋 Next Steps

### **Phase 1: Template Development**
1. **Create article template override** with SP Page Builder integration
2. **Develop helper functions** for dynamic data
3. **Set up custom fields** for author profiles
4. **Test with existing articles**

### **Phase 2: Content Templates**
1. **Create SP Page Builder content templates** for common article types
2. **Develop template selection interface**
3. **Test author workflow**
4. **Refine based on feedback**

### **Phase 3: Migration**
1. **Convert high-priority articles** to hybrid approach
2. **Train content authors** on new workflow
3. **Monitor performance** and user experience
4. **Scale to all articles** based on success

This hybrid approach gives you the **best of both worlds**: the **power of SP Page Builder for content** combined with the **efficiency of template-driven page structure**.
