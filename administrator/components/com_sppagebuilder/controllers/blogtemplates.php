<?php
/**
 * @package SP Page Builder
 * @subpackage Blog Templates Controller
 * <AUTHOR> Development
 */

defined('_JEXEC') or die('Restricted access');

use <PERSON><PERSON><PERSON>\CMS\Factory;
use Joomla\CMS\Language\Text;
use <PERSON><PERSON><PERSON>\CMS\Response\JsonResponse;

class SppagebuilderControllerBlogtemplates extends JControllerLegacy
{
    /**
     * Create a new page from a blog template
     */
    public function createFromTemplate()
    {
        // Check for request forgeries
        JSession::checkToken() or jexit(Text::_('JINVALID_TOKEN'));
        
        $input = Factory::getApplication()->input;
        $templateId = $input->getString('template_id');
        $title = $input->getString('title', 'New Blog Page');
        
        $model = $this->getModel('Page');
        
        try {
            $pageId = $this->createBlogTemplate($templateId, $title, $model);
            
            if ($pageId) {
                $editUrl = 'index.php?option=com_sppagebuilder&view=page&layout=edit&id=' . $pageId;
                $this->setRedirect($editUrl, Text::_('Template created successfully'));
            } else {
                $this->setRedirect('index.php?option=com_sppagebuilder&view=pages', Text::_('Error creating template'), 'error');
            }
        } catch (Exception $e) {
            $this->setRedirect('index.php?option=com_sppagebuilder&view=pages', $e->getMessage(), 'error');
        }
    }
    
    /**
     * Get available blog templates via AJAX
     */
    public function getTemplates()
    {
        $templates = $this->getBlogTemplates();

        echo new JsonResponse($templates);
        Factory::getApplication()->close();
    }

    /**
     * Export template as JSON
     */
    public function exportTemplate()
    {
        $input = Factory::getApplication()->input;
        $pageId = $input->getInt('id');

        if (!$pageId) {
            throw new Exception('No page ID provided');
        }

        $db = Factory::getDbo();
        $query = $db->getQuery(true)
            ->select('title, text')
            ->from('#__sppagebuilder')
            ->where('id = ' . (int) $pageId);

        $page = $db->setQuery($query)->loadObject();

        if (!$page) {
            throw new Exception('Page not found');
        }

        $template = [
            'name' => $page->title,
            'version' => '1.0',
            'data' => json_decode($page->text),
            'created' => date('Y-m-d H:i:s'),
            'type' => 'blog_template'
        ];

        $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $page->title) . '_template.json';

        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        echo json_encode($template, JSON_PRETTY_PRINT);
        Factory::getApplication()->close();
    }

    /**
     * Import template from JSON
     */
    public function importTemplate()
    {
        JSession::checkToken() or jexit(Text::_('JINVALID_TOKEN'));

        $input = Factory::getApplication()->input;
        $files = $input->files->get('template_file');

        if (!$files || $files['error'] !== 0) {
            $this->setRedirect('index.php?option=com_sppagebuilder&view=blogtemplates', 'No file uploaded or upload error', 'error');
            return;
        }

        try {
            $templateData = file_get_contents($files['tmp_name']);
            $template = json_decode($templateData, true);

            if (!$template || !isset($template['data'])) {
                throw new Exception('Invalid template file format');
            }

            $model = $this->getModel('Page');
            $pageId = $model->createBrandNewPage($template['name'], 'com_content', 'category', 0);

            if ($pageId) {
                $db = Factory::getDbo();
                $query = $db->getQuery(true)
                    ->update('#__sppagebuilder')
                    ->set('text = ' . $db->quote(json_encode($template['data'])))
                    ->where('id = ' . (int) $pageId);

                $db->setQuery($query)->execute();

                $editUrl = 'index.php?option=com_sppagebuilder&view=page&layout=edit&id=' . $pageId;
                $this->setRedirect($editUrl, 'Template imported successfully');
            } else {
                throw new Exception('Failed to create page from template');
            }

        } catch (Exception $e) {
            $this->setRedirect('index.php?option=com_sppagebuilder&view=blogtemplates', $e->getMessage(), 'error');
        }
    }
    
    /**
     * Create a blog template page
     */
    private function createBlogTemplate($templateId, $title, $model)
    {
        $templates = $this->getBlogTemplateData();
        
        if (!isset($templates[$templateId])) {
            throw new Exception('Template not found');
        }
        
        $templateData = $templates[$templateId];
        
        // Create new page
        $pageId = $model->createBrandNewPage($title, 'com_content', 'category', 0);
        
        if (!$pageId) {
            throw new Exception('Failed to create page');
        }
        
        // Update with template data
        $db = Factory::getDbo();
        $query = $db->getQuery(true)
            ->update('#__sppagebuilder')
            ->set('text = ' . $db->quote(json_encode($templateData['data'])))
            ->where('id = ' . (int) $pageId);
        
        $db->setQuery($query)->execute();
        
        return $pageId;
    }
    
    /**
     * Get list of available blog templates
     */
    private function getBlogTemplates()
    {
        return [
            [
                'id' => 'blog-with-sidebar',
                'title' => 'Blog with Search Sidebar',
                'description' => 'Blog grid layout with left sidebar search filter. Perfect for content-heavy sites that need advanced filtering.',
                'preview' => 'media/com_sppagebuilder/templates/blog-sidebar-preview.jpg',
                'category' => 'Blog',
                'features' => [
                    'Left sidebar search filter',
                    '3-column grid layout',
                    'AJAX filtering',
                    'Category and tag filters',
                    'Responsive design'
                ]
            ],
            [
                'id' => 'blog-grid-full',
                'title' => 'Full Width Blog Grid',
                'description' => 'Clean full-width grid layout for blog articles. Ideal for showcasing content with maximum visual impact.',
                'preview' => 'media/com_sppagebuilder/templates/blog-grid-preview.jpg',
                'category' => 'Blog',
                'features' => [
                    'Full-width layout',
                    '4-column grid',
                    'Card hover effects',
                    'Featured images',
                    'Clean typography'
                ]
            ],
            [
                'id' => 'blog-featured',
                'title' => 'Blog with Featured Section',
                'description' => 'Blog layout with featured articles section at top. Great for highlighting important content.',
                'preview' => 'media/com_sppagebuilder/templates/blog-featured-preview.jpg',
                'category' => 'Blog',
                'features' => [
                    'Featured articles section',
                    'Dual layout sections',
                    'Priority content display',
                    'Visual hierarchy',
                    'Engagement focused'
                ]
            ],
            [
                'id' => 'blog-minimal',
                'title' => 'Minimal Blog Layout',
                'description' => 'Clean, minimal blog layout with simple cards. Perfect for content-focused sites with clean aesthetics.',
                'preview' => 'media/com_sppagebuilder/templates/blog-minimal-preview.jpg',
                'category' => 'Blog',
                'features' => [
                    'Minimal design',
                    'Centered layout',
                    '2-column grid',
                    'Clean typography',
                    'Focus on content'
                ]
            ]
        ];
    }
    
    /**
     * Get template data structures
     */
    private function getBlogTemplateData()
    {
        return [
            'blog-with-sidebar' => [
                'name' => 'Blog with Search Sidebar',
                'data' => [
                    [
                        'type' => 'section',
                        'id' => 'section-1',
                        'settings' => [
                            'fullscreen' => 0,
                            'background_type' => 'color',
                            'background_color' => '#ffffff',
                            'padding' => '60px 0px 60px 0px'
                        ],
                        'columns' => [
                            [
                                'id' => 'column-1',
                                'settings' => ['md' => 3, 'sm' => 12],
                                'addons' => [
                                    [
                                        'name' => 'blog_grid_cards',
                                        'id' => 'addon-1',
                                        'settings' => [
                                            'title' => '',
                                            'limit' => 12,
                                            'columns' => 1,
                                            'show_featured_image' => 1,
                                            'show_author' => 1,
                                            'show_date' => 1,
                                            'show_readmore' => 1,
                                            'show_filter' => 1,
                                            'filter_style' => 'expanded',
                                            'filter_position' => 'top',
                                            'show_search_box' => 1,
                                            'show_category_filter' => 1,
                                            'show_tag_filter' => 1,
                                            'ajax_filtering' => 1,
                                            'card_shadow' => 1,
                                            'card_hover_effect' => 1
                                        ]
                                    ]
                                ]
                            ],
                            [
                                'id' => 'column-2',
                                'settings' => ['md' => 9, 'sm' => 12],
                                'addons' => [
                                    [
                                        'name' => 'blog_grid_cards',
                                        'id' => 'addon-2',
                                        'settings' => [
                                            'title' => 'Latest Articles',
                                            'limit' => 12,
                                            'columns' => 3,
                                            'show_featured_image' => 1,
                                            'show_author' => 1,
                                            'show_date' => 1,
                                            'show_readmore' => 1,
                                            'show_filter' => 0,
                                            'card_shadow' => 1,
                                            'card_hover_effect' => 1
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            
            'blog-grid-full' => [
                'name' => 'Full Width Blog Grid',
                'data' => [
                    [
                        'type' => 'section',
                        'id' => 'section-1',
                        'settings' => [
                            'fullscreen' => 0,
                            'background_type' => 'color',
                            'background_color' => '#ffffff',
                            'padding' => '60px 0px 60px 0px'
                        ],
                        'columns' => [
                            [
                                'id' => 'column-1',
                                'settings' => ['md' => 12],
                                'addons' => [
                                    [
                                        'name' => 'blog_grid_cards',
                                        'id' => 'addon-1',
                                        'settings' => [
                                            'title' => 'Our Blog',
                                            'limit' => 15,
                                            'columns' => 4,
                                            'show_featured_image' => 1,
                                            'show_author' => 1,
                                            'show_date' => 1,
                                            'show_readmore' => 1,
                                            'show_filter' => 1,
                                            'filter_style' => 'compact',
                                            'filter_position' => 'top',
                                            'show_search_box' => 1,
                                            'show_category_filter' => 1,
                                            'show_tag_filter' => 0,
                                            'ajax_filtering' => 1,
                                            'card_shadow' => 1,
                                            'card_hover_effect' => 1,
                                            'ordering' => 'latest'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            
            'blog-featured' => [
                'name' => 'Blog with Featured Section',
                'data' => [
                    [
                        'type' => 'section',
                        'id' => 'section-1',
                        'settings' => [
                            'fullscreen' => 0,
                            'background_type' => 'color',
                            'background_color' => '#f8f9fa',
                            'padding' => '60px 0px 40px 0px'
                        ],
                        'columns' => [
                            [
                                'id' => 'column-1',
                                'settings' => ['md' => 12],
                                'addons' => [
                                    [
                                        'name' => 'blog_grid_cards',
                                        'id' => 'addon-1',
                                        'settings' => [
                                            'title' => 'Featured Articles',
                                            'limit' => 3,
                                            'columns' => 3,
                                            'show_featured_image' => 1,
                                            'show_author' => 1,
                                            'show_date' => 1,
                                            'show_readmore' => 1,
                                            'ordering' => 'featured',
                                            'card_shadow' => 1,
                                            'card_hover_effect' => 1
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    [
                        'type' => 'section',
                        'id' => 'section-2',
                        'settings' => [
                            'fullscreen' => 0,
                            'background_type' => 'color',
                            'background_color' => '#ffffff',
                            'padding' => '60px 0px 60px 0px'
                        ],
                        'columns' => [
                            [
                                'id' => 'column-2',
                                'settings' => ['md' => 12],
                                'addons' => [
                                    [
                                        'name' => 'blog_grid_cards',
                                        'id' => 'addon-2',
                                        'settings' => [
                                            'title' => 'Recent Articles',
                                            'limit' => 12,
                                            'columns' => 4,
                                            'show_featured_image' => 1,
                                            'show_author' => 1,
                                            'show_date' => 1,
                                            'show_readmore' => 1,
                                            'ordering' => 'latest',
                                            'card_shadow' => 0,
                                            'card_hover_effect' => 1
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            
            'blog-minimal' => [
                'name' => 'Minimal Blog Layout',
                'data' => [
                    [
                        'type' => 'section',
                        'id' => 'section-1',
                        'settings' => [
                            'fullscreen' => 0,
                            'background_type' => 'color',
                            'background_color' => '#ffffff',
                            'padding' => '80px 0px 80px 0px'
                        ],
                        'columns' => [
                            [
                                'id' => 'column-1',
                                'settings' => ['md' => 8, 'md_offset' => 2],
                                'addons' => [
                                    [
                                        'name' => 'blog_grid_cards',
                                        'id' => 'addon-1',
                                        'settings' => [
                                            'title' => 'Blog',
                                            'limit' => 9,
                                            'columns' => 2,
                                            'show_featured_image' => 1,
                                            'show_author' => 0,
                                            'show_date' => 1,
                                            'show_readmore' => 0,
                                            'show_filter' => 1,
                                            'filter_style' => 'minimal',
                                            'filter_position' => 'top',
                                            'show_search_box' => 1,
                                            'show_category_filter' => 0,
                                            'show_tag_filter' => 0,
                                            'ajax_filtering' => 1,
                                            'card_shadow' => 0,
                                            'card_hover_effect' => 1,
                                            'card_border' => '#f0f0f0'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
